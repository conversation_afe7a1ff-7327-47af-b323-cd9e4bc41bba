# Ollama G Drive Startup Script
# 将Ollama配置为使用G盘存储

Write-Host "========================================" -ForegroundColor Green
Write-Host "Ollama Server - G Drive Configuration" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:OLLAMA_MODELS = "G:\ollama\models"
$env:OLLAMA_HOME = "G:\ollama\.ollama"

Write-Host "Setting environment variables..." -ForegroundColor Yellow
Write-Host "OLLAMA_MODELS: $env:OLLAMA_MODELS" -ForegroundColor Cyan
Write-Host "OLLAMA_HOME: $env:OLLAMA_HOME" -ForegroundColor Cyan
Write-Host ""

# 检查并创建目录
Write-Host "Checking G drive directories..." -ForegroundColor Yellow
if (!(Test-Path "G:\ollama\models")) {
    Write-Host "Creating models directory..." -ForegroundColor Yellow
    New-Item -Path "G:\ollama\models" -ItemType Directory -Force | Out-Null
}
if (!(Test-Path "G:\ollama\.ollama")) {
    Write-Host "Creating .ollama directory..." -ForegroundColor Yellow
    New-Item -Path "G:\ollama\.ollama" -ItemType Directory -Force | Out-Null
}

Write-Host "Directories ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Starting Ollama server..." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Red
Write-Host ""

# 启动Ollama服务
try {
    ollama serve
} catch {
    Write-Host "Error starting Ollama: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}

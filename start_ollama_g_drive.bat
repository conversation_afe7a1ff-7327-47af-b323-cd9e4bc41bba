@echo off
title Ollama Server (G Drive)
echo ========================================
echo Ollama Server - G Drive Configuration
echo ========================================
echo.

echo Setting Ollama environment variables for G drive...
set OLLAMA_MODELS=G:\ollama\models
set OLLAMA_HOME=G:\ollama\.ollama

echo OLLAMA_MODELS=%OLLAMA_MODELS%
echo OLLAMA_HOME=%OLLAMA_HOME%
echo.

echo Checking G drive directories...
if not exist "G:\ollama\models" (
    echo Creating models directory...
    mkdir "G:\ollama\models"
)
if not exist "G:\ollama\.ollama" (
    echo Creating .ollama directory...
    mkdir "G:\ollama\.ollama"
)

echo.
echo Starting Ollama server...
echo Press Ctrl+C to stop the server
echo.
ollama serve
